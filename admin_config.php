<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EoDB Chatbot Admin Configuration</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .nav-tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #ddd;
        }
        .nav-tab {
            padding: 15px 20px;
            cursor: pointer;
            border-bottom: 3px solid transparent;
            transition: all 0.3s;
        }
        .nav-tab.active {
            background: white;
            border-bottom-color: #007bff;
            color: #007bff;
        }
        .nav-tab:hover {
            background: #e9ecef;
        }
        .tab-content {
            display: none;
            padding: 20px;
        }
        .tab-content.active {
            display: block;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        .form-group input, .form-group textarea, .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .form-group textarea {
            height: 100px;
            resize: vertical;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
        }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn:hover { opacity: 0.9; }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .table th, .table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .table th {
            background: #f8f9fa;
            font-weight: bold;
        }
        .table tr:hover {
            background: #f5f5f5;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        .status-active { background: #d4edda; color: #155724; }
        .status-inactive { background: #f8d7da; color: #721c24; }
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }
        .alert-success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .alert-error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .config-section {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .config-section h3 {
            margin-top: 0;
            color: #495057;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>EoDB Chatbot Admin Configuration</h1>
            <p>Manage all dynamic settings for the chatbot</p>
        </div>
        
        <div class="nav-tabs">
            <div class="nav-tab active" onclick="showTab('greeting')">Greeting Messages</div>
            <div class="nav-tab" onclick="showTab('options')">Option Status</div>
            <div class="nav-tab" onclick="showTab('keywords')">Keyword Redirects</div>
            <div class="nav-tab" onclick="showTab('commands')">Special Commands</div>
            <div class="nav-tab" onclick="showTab('system')">System Config</div>
        </div>
        
        <!-- Greeting Messages Tab -->
        <div id="greeting" class="tab-content active">
            <h2>Greeting Messages</h2>
            <div class="config-section">
                <h3>Main Greeting Messages</h3>
                <div class="form-group">
                    <label for="initial_greeting">Initial Greeting:</label>
                    <textarea id="initial_greeting" placeholder="Namaskar ! I am your Virtual Assistant for AI Sanlaap !"></textarea>
                </div>
                <div class="form-group">
                    <label for="service_help_text">Service Help Text:</label>
                    <textarea id="service_help_text" placeholder="I can help you with the following services:"></textarea>
                </div>
                <div class="form-group">
                    <label for="general_query_prompt">General Query Prompt:</label>
                    <textarea id="general_query_prompt" placeholder="If you have any other queries, you may type here ........"></textarea>
                </div>
                <div class="form-group">
                    <label for="coming_soon_message">Coming Soon Message:</label>
                    <textarea id="coming_soon_message" placeholder="This feature is coming soon. Please use the text box below to ask your queries."></textarea>
                </div>
                <button class="btn btn-primary" onclick="saveGreetingMessages()">Save Greeting Messages</button>
            </div>
            
            <div class="config-section">
                <h3>Prompt Messages</h3>
                <div class="form-group">
                    <label for="caf_prompt">CAF Number Prompt:</label>
                    <input type="text" id="caf_prompt" placeholder="Please enter your CAF number:">
                </div>
                <div class="form-group">
                    <label for="otp_prompt">OTP Prompt:</label>
                    <input type="text" id="otp_prompt" placeholder="Please enter the OTP you received:">
                </div>
                <div class="form-group">
                    <label for="exit_message">Exit Message:</label>
                    <input type="text" id="exit_message" placeholder="Thank you for using AI Sanlaap! The chatbot session has been ended.">
                </div>
                <button class="btn btn-primary" onclick="savePromptMessages()">Save Prompt Messages</button>
            </div>
        </div>
        
        <!-- Option Status Tab -->
        <div id="options" class="tab-content">
            <h2>Option Status Management</h2>
            <div class="config-section">
                <h3>Main Menu Options</h3>
                <table class="table">
                    <thead>
                        <tr>
                            <th>Option</th>
                            <th>Name</th>
                            <th>Status</th>
                            <th>Disabled Message</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="options-table">
                        <!-- Dynamic content will be loaded here -->
                    </tbody>
                </table>
                <button class="btn btn-success" onclick="addNewOption()">Add New Option</button>
            </div>
        </div>
        
        <!-- Keywords Tab -->
        <div id="keywords" class="tab-content">
            <h2>Keyword Redirects</h2>
            <div class="config-section">
                <h3>Manage Keyword Redirects</h3>
                <table class="table">
                    <thead>
                        <tr>
                            <th>Keyword</th>
                            <th>Redirect To Step</th>
                            <th>Redirect Option</th>
                            <th>Message</th>
                            <th>Priority</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="keywords-table">
                        <!-- Dynamic content will be loaded here -->
                    </tbody>
                </table>
                <button class="btn btn-success" onclick="addNewKeyword()">Add New Keyword</button>
            </div>
        </div>
        
        <!-- Special Commands Tab -->
        <div id="commands" class="tab-content">
            <h2>Special Commands</h2>
            <div class="config-section">
                <h3>Manage Special Commands (exit, restart, etc.)</h3>
                <table class="table">
                    <thead>
                        <tr>
                            <th>Command</th>
                            <th>Type</th>
                            <th>Response Message</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="commands-table">
                        <!-- Dynamic content will be loaded here -->
                    </tbody>
                </table>
                <button class="btn btn-success" onclick="addNewCommand()">Add New Command</button>
            </div>
        </div>
        
        <!-- System Config Tab -->
        <div id="system" class="tab-content">
            <h2>System Configuration</h2>
            <div class="config-section">
                <h3>General Settings</h3>
                <div class="form-group">
                    <label for="chatbot_name">Chatbot Name:</label>
                    <input type="text" id="chatbot_name" placeholder="AI Sanlaap">
                </div>
                <div class="form-group">
                    <label for="api_timeout">API Timeout (seconds):</label>
                    <input type="number" id="api_timeout" placeholder="30">
                </div>
                <div class="form-group">
                    <label for="max_session_duration">Max Session Duration (seconds):</label>
                    <input type="number" id="max_session_duration" placeholder="3600">
                </div>
                <div class="form-group">
                    <label for="default_language">Default Language:</label>
                    <select id="default_language">
                        <option value="english">English</option>
                        <option value="bengali">Bengali</option>
                    </select>
                </div>
                <button class="btn btn-primary" onclick="saveSystemConfig()">Save System Config</button>
            </div>
            
            <div class="config-section">
                <h3>API Endpoints</h3>
                <div class="form-group">
                    <label for="caf_api_url">CAF API URL:</label>
                    <input type="text" id="caf_api_url" placeholder="http://localhost:8000/api/caf">
                </div>
                <div class="form-group">
                    <label for="rag_api_url">RAG API URL:</label>
                    <input type="text" id="rag_api_url" placeholder="http://localhost:8000/api/rag">
                </div>
                <button class="btn btn-primary" onclick="saveApiConfig()">Save API Config</button>
            </div>
        </div>
    </div>

    <script>
        // Configuration for API endpoint
        const API_BASE = 'http://localhost:8000';
        
        function showTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.nav-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // Show selected tab
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
            
            // Load data for the selected tab
            loadTabData(tabName);
        }
        
        function loadTabData(tabName) {
            switch(tabName) {
                case 'greeting':
                    loadGreetingMessages();
                    break;
                case 'options':
                    loadOptionStatus();
                    break;
                case 'keywords':
                    loadKeywordRedirects();
                    break;
                case 'commands':
                    loadSpecialCommands();
                    break;
                case 'system':
                    loadSystemConfig();
                    break;
            }
        }
        
        function showAlert(message, type = 'success') {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type}`;
            alertDiv.textContent = message;
            
            // Insert at the top of the active tab
            const activeTab = document.querySelector('.tab-content.active');
            activeTab.insertBefore(alertDiv, activeTab.firstChild);
            
            // Remove after 5 seconds
            setTimeout(() => {
                alertDiv.remove();
            }, 5000);
        }
        
        // Placeholder functions for CRUD operations
        function loadGreetingMessages() {
            // TODO: Load from database via API
            console.log('Loading greeting messages...');
        }
        
        function saveGreetingMessages() {
            // TODO: Save to database via API
            showAlert('Greeting messages saved successfully!');
        }
        
        function savePromptMessages() {
            // TODO: Save to database via API
            showAlert('Prompt messages saved successfully!');
        }
        
        function loadOptionStatus() {
            // TODO: Load from database via API
            console.log('Loading option status...');
        }
        
        function loadKeywordRedirects() {
            // TODO: Load from database via API
            console.log('Loading keyword redirects...');
        }
        
        function loadSpecialCommands() {
            // TODO: Load from database via API
            console.log('Loading special commands...');
        }
        
        function loadSystemConfig() {
            // TODO: Load from database via API
            console.log('Loading system config...');
        }
        
        function saveSystemConfig() {
            // TODO: Save to database via API
            showAlert('System configuration saved successfully!');
        }
        
        function saveApiConfig() {
            // TODO: Save to database via API
            showAlert('API configuration saved successfully!');
        }
        
        function addNewOption() {
            // TODO: Show modal for adding new option
            showAlert('Add new option functionality coming soon!', 'info');
        }
        
        function addNewKeyword() {
            // TODO: Show modal for adding new keyword
            showAlert('Add new keyword functionality coming soon!', 'info');
        }
        
        function addNewCommand() {
            // TODO: Show modal for adding new command
            showAlert('Add new command functionality coming soon!', 'info');
        }
        
        // Initialize the page
        $(document).ready(function() {
            loadTabData('greeting');
        });
    </script>
</body>
</html>
