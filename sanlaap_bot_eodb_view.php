<style>
  .options-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    /* center buttons horizontally */
    gap: 10px;
    /* space between buttons */
    margin-top: 10px;
  }

  .option_btn {
    width: 200px;
    /* or auto, if you want them to size to content */
    padding: 5px 10px;
    font-size: 10px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    /*background: linear-gradient(145deg, #6e7bff, #4f5bff);*/
    background: linear-gradient(65deg, #24bcedc4,rgba(20, 158, 149, 0.4)); 
    color: black;
    font-weight: bold;
    text-align: center;
  }

  .option_btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
  }

  .option_btn:active {
    transform: translateY(1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }

  .option_btn.yes_btn {
    background: linear-gradient(145deg, #56e39f, #2c9f4b);
  }

  .option_btn.no_btn {
    background: linear-gradient(145deg, #f76c6c, #d32f2f);
  }


  /*************** */
  /*.option_btn  {
    width: 200px;
  padding: 10px 20px;
  margin: 10px;
  font-size: 10px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  color: white;
  font-weight: bold;
}

.option_btn :hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.option_btn :active {
  transform: translateY(1px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.option_btn.yes_btn {
  background: linear-gradient(145deg, #56e39f, #2c9f4b);
}

.option_btn.no_btn {
  background: linear-gradient(145deg, #f76c6c, #d32f2f);
}*/
</style>
<style>
  .dislike {
    font-size: 20px;
    outline: none;
    color: #0080df;
  }

  .chatbox__image--header {
    width: 11%;
    height: 100%;
    border: 1px solid #000;
    background: #f5eb5c;
    border-radius: 50%;
    margin-right: 10px;
  }

  .chatbox__image--header img {
    width: 100%;
  }

  .chatbot {
    width: 10%;
  }

  .chatbot img {
    width: 62px;
    background: #4089e7;
    float: right;
    border-radius: 50%;
    height: 62px;
    object-fit: contain;
    position: fixed;
    right: 79px;
    z-index: 99999;
    bottom: 60px;
    border: 2px solid #007b95;
  }

  .dislike-btn {
    transform: scaleX(-1);
  }

  .like {
    font-size: 20px;
    outline: none;
    color: #0080df;
  }

  .like-pop {
    font-size: 45px;
    outline: none;
    color: #0080df;
  }

  .chatbox__messages {
    height: 400px;
    overflow-y: auto;
  }

  .messages__parent {
    position: relative;
    padding-bottom: 30px;
  }

  .messages__item--loader {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    justify-content: center;
    color: #007bff;
  }

  .messages__item--loader .dot {
    width: 8px;
    height: 8px;
    background-color: #333;
    border-radius: 50%;
    margin: 0 3px;
    animation: dot-blink 1s infinite ease-in-out;
    color: #007bff;
  }

  @keyframes dot-blink {
    0% {
      opacity: 0;
    }

    50% {
      opacity: 1;
    }

    100% {
      opacity: 0;
    }
  }

  /*************chatbot*************** */

  .messages__item--loader {
    display: flex;
    justify-content: center;
    align-items: center;
    height: auto;
    margin: 10px 0;
  }

  .messages__item--loader .dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: gray;
    animation: blink 1.4s infinite;
  }

  .messages__item--loader .dot:nth-child(2) {
    animation-delay: 0.2s;
  }

  .messages__item--loader .dot:nth-child(3) {
    animation-delay: 0.4s;
  }

  @keyframes blink {

    0%,
    80%,
    100% {
      opacity: 0;
    }

    40% {
      opacity: 1;
    }
  }

  .loader {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 5px;
    margin: 10px 0;
  }

  .dot {
    width: 8px;
    height: 8px;
    background-color: #007bff;
    border-radius: 50%;
    animation: bounce 1.5s infinite ease-in-out;
  }

  .dot:nth-child(2) {
    animation-delay: 0.3s;
  }

  .dot:nth-child(3) {
    animation-delay: 0.6s;
  }

  @keyframes bounce {

    0%,
    80%,
    100% {
      transform: scale(0);
      opacity: 0.5;
    }

    40% {
      transform: scale(1);
      opacity: 1;
    }
  }

  .lng-box {
    background-color: #f1f1f1;
    padding: 10px;
    border-radius: 5px;
    position: absolute;
    top: 30%;
    left: 0;
    right: 0;
    width: 300px;
    margin: 0 auto;
    text-align: center;
  }

  .language-selection {
    display: block;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: #0008;
    height: 100vh;
    z-index: 99999999999999;
  }

  #language {
    margin-bottom: 5px;
  }

  .messages__icon {
    margin-right: 10px;
  }

  .chatbox__support {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .messages__parent {
    width: 343px;
  }

  .chatbox__messages {
    flex-grow: 1;
    overflow-y: auto;
    padding: 10px;
    border-bottom: 1px solid #ccc;
  }

  .main-suggestions {
    background-color: #f9f9f9;
    padding: 10px;
    border-top: 1px solid #ccc;
    max-height: 100px;
    overflow-y: auto;
    width: 100%;
  }

  .chatbox__footer {
    padding: 10px;
    display: flex;
    align-items: center;
  }

  /* chatbot */
  .main-suggestions {
    margin-top: 10px;
    padding: 0 15px;
  }

    .disabled {
    pointer-events: none;
    opacity: 0.5;
    cursor: not-allowed;
  }

  .suggestion-item {
    border-radius: 20px;
    text-align: left;
    overflow-wrap: break-word;
    background: linear-gradient(65deg, #33b1be80, #32c5c01a) !important;
    color: #333;
    font-weight: 550;
    border: 1px solid rgb(7 203 223);
    min-height: 38px;
    font-size: 12px;
    padding: 4px 10px;
    color: #000;
    margin-bottom: 5px;
    display: flex;
    align-items: center;
    cursor: pointer;
  }

  .suggestion-item:hover {
    background-color: #d4d4d4;
  }

  .suggestion-item.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: #f0f0f0 !important;
    color: #999 !important;
  }

  .suggestion-item.disabled:hover {
    background: #f0f0f0 !important;
  }

  .messages__item--visitor {
    float: right;
    width: auto;
  }

  .messages__wrapper {
    width: 100%;
    display: inline-block;
  }

  .messages__item--operator {
    float: left;
  }

  /*chatbox*/
  .chatbox {
    position: fixed;
    right: 0;
    bottom: 5px;
    max-width: 400px;
    width: 100%;
    border-radius: 22px;
    overflow: hidden;
    box-shadow: rgba(50, 50, 93, 0.25) 0px 30px 60px -12px, rgba(0, 0, 0, 0.3) 0px 18px 36px -18px;
    z-index: 99999;
    display: none;
  }

  .chatbox__support {
    background: #f9f9f9;
    height: 600px;
    width: 100%;
    box-shadow: 0px 0px 15px rgba(0, 0, 0, 0.1);
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    overflow: hidden;
    transition: all 0.5s ease-in-out;
    transform: translateY(0);
    opacity: 1;
  }

  .chatbox--active .chatbox__support {
    transform: translateY(-40px);
    z-index: 123456;
    opacity: 1;
  }

  .chatbox__header {
    background: linear-gradient(65deg, #42d2e1, #32c5c01a) !important;
    /* background: linear-gradient(52deg, #003156 0.52%, #006262 100%); */
    /* background:linear-gradient(93.12deg, #054a95 0.52%, #0452a7 100%); */
    /*background: linear-gradient(93.12deg, #581b98 0.52%, #9c1de7 100%);*/
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 5px 20px;
    border-top-left-radius: 20px;
    border-top-right-radius: 20px;
    box-shadow: 0px 10px 15px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    color: #000;
  }

  .chatbox__image--header img {
    margin-right: 10px;
    border-radius: 50%;
  }

  .chatbox__heading--header {
    font-size: 1.2rem;
    color: #000;
    margin: 0;
  }

  .chatbox__description--header {
    font-size: 0.9rem;
    color: #000;
    margin: 0;
  }

  .chatbox__messages {
    padding: 20px;
    overflow-y: auto;
    height: calc(100% - 142px);
    background-image: url('assets/site_resources/images/e-nathikaran/silver-abstract-background.jpg');
    background-size: cover;
  }

  .messages__item {
    margin-bottom: 10px;
    padding: 10px;
    border-radius: 10px;
    max-width: 70%;
    word-wrap: break-word;
  }

  .messages__item--visitor {
    background-color: #4dc6da;
    align-self: flex-start;
    color: #000000;
    font-size: 13px;
    font-weight: 600;
    background: linear-gradient(65deg, #24bcedc4, #64a5a166) !important;
    border-top-left-radius: 20px;
    border-bottom-right-radius: 20px;
    border-bottom-left-radius: 20px;
  }

  .messages__item--operator {
    background-color: #fff;
    color: #000;
    align-self: flex-end;
    font-size: 13px;
    box-shadow: 2px 3px 12px #00000040;
    text-align: justify;
  }

  .chatbox__footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 20px;
    /*background: linear-gradient(268.91deg, #581b98 -2.14%, #9c1de7 99.69%);*/
    /* background: linear-gradient(268.91deg, #054a95 -2.14%, #0452a7 99.69%); */
    /* background: linear-gradient(52deg, #003156 0.52%, #006262 100%); */
    background: linear-gradient(52deg, #5494c5 0.52%, #1f7878 100%);
    box-shadow: 0px -10px 15px rgba(0, 0, 0, 0.1);
    border-bottom-left-radius: 20px;
    border-bottom-right-radius: 20px;
    background-color: #fff;
  }

  .chatbox__footer input[type="text"] {
    flex: 1;
    border: none;
    padding: 10px;
    border-radius: 30px;
    outline: none;
    font-size: 14px;
    margin-right: 3px;
    border: 1px solid #0002;
  }

  .chatbox__send--footer {
    background-color: white;
    color: var(--primary);
    padding: 10px 20px;
    border-radius: 50px;
    border: none;
    outline: none;
    cursor: pointer;
    transition: background-color 0.3s ease;
  }

  .chatbox__send--footer:hover {
    background-color: #f0f0f0;
  }

  .chatbox__button {
    position: absolute;
    bottom: 20px;
    right: 20px;
    z-index: 999999;
  }

  .chatbox__button button {
    background-color: transparent;
    border: none;
    outline: none;
    cursor: pointer;
  }

  .close {
    cursor: pointer;
  }

  .btn-whatsapp-pulse .fa {
    font-size: 20px;
  }

  .btn-whatsapp-pulse {
    /*background: #016ecd;*/
    color: white;
    position: fixed;
    bottom: 61px;
    right: 80px;
    /* font-size: 142px; */
    display: flex;
    justify-content: center;
    align-items: center;
    width: 0;
    height: 0;
    padding: 30px;
    text-decoration: none;
    border-radius: 50%;
    animation-name: pulse;
    animation-duration: 1.5s;
    animation-timing-function: ease-out;
    animation-iteration-count: infinite;
    border: 1px solid #a2a2a2;
  }

  .btn-whatsapp-pulse:hover {
    color: #ecc233;
  }

  @keyframes pulse {
    0% {
      box-shadow: 0 0 0 0 rgba(37, 211, 102, 0.5);
    }

    80% {
      box-shadow: 0 0 0 14px rgba(37, 211, 102, 0);
    }
  }

  .btn-whatsapp-pulse-border {
    bottom: 120px;
    right: 20px;
    animation-play-state: paused;
  }

  .btn-whatsapp-pulse-border::before {
    content: "";
    position: absolute;
    border-radius: 50%;
    padding: 25px;
    border: 5px solid #25d366;
    opacity: 0.75;
    animation-name: pulse-border;
    animation-duration: 1.5s;
    animation-timing-function: ease-out;
    animation-iteration-count: infinite;
  }

  @keyframes pulse-border {
    0% {
      padding: 25px;
      opacity: 0.75;
    }

    75% {
      padding: 50px;
      opacity: 0;
    }

    100% {
      opacity: 0;
    }
  }

  /*chatbox-end*/
  .default-query-dropdown {
    color: white;
    text-align: left;
    width: 100%;
    position: relative;
    z-index: 10;
    text-align: center;
  }

  /* Dropdown Button */
  .default-query-button {
    background-color: #ecbefa;
    color: white;
    border: none;
    padding: 0px;
    width: 100%;
    font-size: 10px;
    text-align: left;
    cursor: pointer;
  }

  .default-query-button:hover {
    background-color: #004494;
  }

  /* Dropdown Content */
  .dropdown-content {
    display: none;
    position: absolute;
    left: 0;
    width: 100%;
    background-color: white;
    border: 1px solid #ddd;
    box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
    z-index: 20;
  }

  .dropdown-content button {
    border-radius: 20px;
    text-align: center;
    overflow-wrap: break-word;
    background: linear-gradient(65deg, #33b1be80, #32c5c01a) !important;
    color: #333;
    font-weight: 550;
    border: 1px solid rgb(7 203 223);
    min-height: 38px;
    white-space: break-spaces;
    font-size: 12px;
    padding: 4px 10px;
    background-color: white;
    color: black;
    padding: 10px;
    border: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
    font-size: 14px;
  }

  .default_suggestion {
    background-color: #fff;
    padding: 10px 15px;
    border-radius: 0px 20px 20px 20px;
    box-shadow: 0 1px 3px 0 #cdcdcd !important;
    margin: 10px;
  }

  .dropdown-content button:hover {
    background-color: #f1f1f1;
    /* Highlighted on hover */
  }

  /* Show dropdown on hover */
  .default-query-dropdown:hover .dropdown-content {
    display: block;
  }

  .e-nathikaran img {
    transition: 0.5s ease-in-out;
  }

  .e-nathikaran img:hover {
    transform: translateY(-5px);
  }

  /*reaction*/

  .messages__wrapper {
    margin-bottom: 10px;
  }

  .messages__item {
    padding: 10px;
    border-radius: 12px 12px 0px 12px;
    background-color: #f1f1f1;
    position: relative;
  }

  .messages__item--sender {
    background-color: #d0eaff;
  }

  .messages__item--operator {
    background-color: #fff;
    /* font-style: italic;*/
    border-radius: 0px 12px 12px 12px;
  }

  .operator-icon {
    position: absolute;
    left: -30px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 20px;
  }

  .reactions-container {
    margin-top: 5px;
  }

  .like-btn,
  .dislike-btn {
    background: none;
    border: none;
    cursor: pointer;
    color: #555;
  }

  .like-btn:hover,
  .dislike-btn:hover {
    color: #007bff;
  }

  /* Modal styles */
  .reaction-modal {
    display: none;
    position: fixed;
    z-index: 1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4);
    overflow: auto;
    padding-top: 60px;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  }

  .reaction-modal-content {
    background-color: #fefefe;
    margin: 5% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
    border-radius: 20px;
  }

  .close-btn {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
  }

  .close-btn:hover,
  .close-btn:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
  }

  .chat-form label {
    font-weight: bold;
    font-size: 12px;
  }

  .chat-form input {
    font-size: 12px;
  }

  .chat-form .form-group {
    margin: 3px 0;
    margin-top: 5px;
  }
</style>
<!-- jQuery UI CSS -->
<link rel="stylesheet" href="https://code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css">

<!-- jQuery UI and jQuery JS -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.js"></script>
<?php
$this->load->view("templates/header_view2");
//$this->load->view("templates/header_view");
?>

<?php
//$this->load->view("templates/footer_view");
?>
<!-- <a href="javascript:void(0)" class="btn-whatsapp-pulse"> -->
<a href="javascript:void(0)" class="chatbot btn-whatsapp-pulse">
  <!-- <i class="fa fa-comments" aria-hidden="true"></i> -->
  <img src="<?php echo base_url('assets/site_resources/images/chat.png'); ?>" alt="Avatar">
</a>
<!-- Language Selection Section -->
<div class="language-selection" style="display: none;">
  <div class="lng-box">
    <label for="language">Select Language:</label>
    <select id="language" class="form-control">
      <option value="1">English</option>
      <option value="2">Bengali</option>
    </select>
    <button class="btn-select-language btn btn-primary" id="select_L_B">Select</button>
  </div>
</div>
<div class="chatbox">
  <div class="chatbox__support">
    <div class="chatbox__header">
      <div class="d-flex" style="align-items:center">
        <div class="chatbox__image--header">
          <img src="<?php echo base_url('assets/site_resources/images/chat.png'); ?>" alt="Avatar">
        </div>
        <div class="chatbox__content--header">
          <h4 class="chatbox__heading--header">AI Sanlaap(EoDB)</h4>
          <p class="chatbox__description--header">Hi,How can I help you?</p>
        </div>
      </div>
      <div class="close">
        <i class="fa fa-times" aria-hidden="true"></i>
      </div>
    </div>
    <!-- Slim Dropdown for Default Query -->
    <div class="default-query-dropdown">
    </div>
    <div class="chatbox__messages" id="chat-messages">
      <div id="default_suggestion" class="default_suggestion">
        <p style="font-size: 11px; width: 100%; color: #000; font-weight: 400;margin-bottom: 10px;text-align:left"><b>Namaskar ! I am
            your Virtual Assistant for AI Sanlaap !
            <p style="font-size: 11px; width: 100%; color: #000; font-weight: 400;margin-bottom: 10px;white-space: normal;text-align:left">
              I can help you with the following services:
          </b></p>

        <div class="suggestion-item" onclick="selectQuestion('1. Apply for licence/clearance')" id="apply_licence">1. Apply for licence/clearance</div>
        <div class="suggestion-item" onclick="selectQuestion('2. Know application status')" id="know_status">2. Know application status</div>
        <div class="suggestion-item disabled" onclick="showComingSoon('3. Raise any query / Grievance')" id="raise_query">3. Raise any query / Grievance</div>
        <div class="suggestion-item disabled" onclick="showComingSoon('4. FAQ')" id="faq">4. FAQ</div>
        <p style="font-size: 11px; width: 100%; color: #000; font-weight: 400;margin-bottom: 10px;white-space: normal;text-align:left"><b>
            If you have any other queries, you may type here ........
          </b></p>
      </div>
      <!-- chatbox form start-->
      <!-- <div id="form_div" class="default_suggestion chat-form" hidden>
        <form action="">
          <div class="form-group">
            <label>Name</label>
            <input type="text" class="form-control" name="" placeholder="Enter name">
          </div>
          <div class="form-group">
            <label>Email</label>
            <input type="text" class="form-control" name="" placeholder="Enter email">
          </div>
          <div class="form-group">
            <label>Mobile No.</label>
            <input type="text" class="form-control" name="" placeholder="Enter mobile no">
          </div>
          <div class="form-group text-center mt-4">
            <button class="btn btn-primary btn-sm">Submit</button>
          </div>
        </form>
      </div> -->
      <!-- chatbox form End-->
      <div id="reactionModalD" class="reaction-modal">
        <div class="reaction-modal-content text-center">
          <!--   <span class="close-btn">&times;</span> -->
          <input type="hidden" id="dataR">
          <h3 style="font-weight:bold;">Sorry for your inconvenience.</h3>
          <p style="text-align: center;">Please share your feedback below. It will help me to improve myself.</p>
          <textarea id="feedback" name="feedback" class="form-control" style="margin:7px" placeholder="Enter your feedback ...."></textarea>
          <button id="reaction_submit" class="btn btn-primary mt-2 btn-sm" onclick="reactionSubmit()">Submit</button>
          <button class="btn btn-danger mt-2 btn-sm" onclick="cancel()">Cancel</button>
        </div>
      </div>
      <div id="reactionModal" class="reaction-modal">
        <div class="reaction-modal-content text-center">
          <h1><i class="fa-solid fa-thumbs-up like-pop"></i></h1>
        </div>
      </div>

      <span id="c_loader" style="display:none;"><img src="<?php echo base_url('assets/site_resources/images/Animation.gif'); ?>" style="  width: 80px;
          position: absolute;
          bottom: 40px;
        left: 22px;" alt=""></span>
    </div>
    <!-- Suggestions container moved outside of chatbox__messages -->
    <div class="suggestions">
      <!-- Suggested questions will go here -->
    </div>
    <div class="chatbox__footer">
      <a href="">
        <img id="lang" style="width:35px;margin-right:8px" src="<?php base_url() ?>assets/site_resources/images/hindi.png" alt="">
      </a>
      <!-- <textarea id="question" style="border-radius: 25px;resize: none;box-sizing: border-box;" placeholder="Ask a question..." autocomplete="off" rows="" cols="40"></textarea> -->
      <input type="text" id="question" placeholder="Ask a question..." autocomplete="off">
      <a href="" style="margin:0px 5px;font-size:20px;color:#ffa700"><i class="fa fa-microphone" aria-hidden="true"></i></a>
      <button class="chatbox__send--footer" onclick="askQuestion()" id="send_button">
        <svg width="57px" height="54px" viewBox="1496 193 57 54" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="width: 18px; height: 18px;">
          <g id="Group-9-Copy-3" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" transform="translate(1523.000000, 220.000000) rotate(-270.000000) translate(-1523.000000, -220.000000) translate(1499.000000, 193.000000)">
            <path d="M5.42994667,44.5306122 L16.5955554,44.5306122 L21.049938,20.423658 C21.6518463,17.1661523 26.3121212,17.1441362 26.9447801,20.3958097 L31.6405465,44.5306122 L42.5313185,44.5306122 L23.9806326,7.0871633 L5.42994667,44.5306122 Z M22.0420732,48.0757124 C21.779222,49.4982538 20.5386331,50.5306122 19.0920112,50.5306122 L1.59009899,50.5306122 C-1.20169244,50.5306122 -2.87079654,47.7697069 -1.64625638,45.2980459 L20.8461928,-0.101616237 C22.1967178,-2.8275701 25.7710778,-2.81438868 27.1150723,-0.101616237 L49.6075215,45.2980459 C50.8414042,47.7885641 49.1422456,50.5306122 46.3613062,50.5306122 L29.1679835,50.5306122 C27.7320366,50.5306122 26.4974445,49.5130766 26.2232033,48.1035608 L24.0760553,37.0678766 L22.0420732,48.0757124 Z" id="sendicon" fill="#96AAB4" fill-rule="nonzero"></path>
          </g>
        </svg>
      </button>
    </div>
  </div>
</div>
<script>
  const lenis = new Lenis()
  lenis.on('scroll', (e) => {
    console.log(e)
  })

  function raf(time) {
    lenis.raf(time)
    requestAnimationFrame(raf)
  }
  requestAnimationFrame(raf)
</script>
<!-- <div style="margin-top: 100vh;"></div> -->
<?php
//$this->load->view("templates/footer_view2");
?>
<script>
  /*******chatbotopen**** */
  $('.btn-whatsapp-pulse').click(function() {
    disabledSendSection();
    $('.chatbox').css('display', 'block');
    //Request for Session id 
    $.ajax({
      url: "<?php echo base_url('super_admin/ChatbotEntryController/sessionCreateEODB'); ?>", // Replace with your CodeIgniter route for suggestions
      type: "POST",
      dataType: "json",
      /*contentType: "application/json",*/
      data: {
        type: "session_request"

      },
      success: function(data) {
        console.log("response=" + data)
        $("#result").html(`<p>Status: ${data.status}</p>`);
      },
      error: function(error) {
        console.error("Error:", error);
        $("#result").html("<p>An error occurred.</p>");
      }
    });

  });
  $('.fa-times').click(function() {
    $('.chatbox').css('display', 'none');
  });

  function debounce(func, delay) {
    let timeout;
    return function(...args) {
      console.log("args=" + JSON.stringify(args));
      const context = this;
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(context, args), delay);
    };
  }
  /***********chatbot********** */
  function toggleChatbox() {
    const chatbox = $('#chatbox');
    const chatLang = $('.chat-lang');
    const chaticon = $('.icon-chat');
    const isChatboxVisible = chatbox.is(':visible');
    chatbox.toggle(!isChatboxVisible);
    chatLang.toggle(!isChatboxVisible);
    chaticon.toggleClass('hidden');
  }

  function showChatb() {
    $('.chat-lang').hide();
    $('#chatbox').show();
  }
  $('#lang').click(function(event) {
    event.preventDefault(); // Prevent the default behavior
    $('.language-selection').show();
  });
  $(document).ready(function() {
    $("#c_loader").hide();
    $("#c_loader").css('display', 'none');
    // Trigger askQuestion on Enter key press when in the #question input field
    $("#question").on("keydown", function(e) {
      if (e.key === "Enter") { // Check if Enter key is pressed
        /*qu = $(this).val().trim();
        if (qu) {
        askQuestion();
        } else {
        //alert();
        }*/
        askQuestion();
      }
    });
  });
  let selectedLanguage = null; // Initialize variable to hold the selected language
  $('.btn-whatsapp-pulse').click(function() {
    // Show the language selection section
    //$('.language-selection').css('display', 'block');
  });
  // Handle the language selection
  $('.btn-select-language').click(function() {
    // Get the selected language
    //const language = $('#language').val();
    const language = "1";
    // Hide the language selection section
    $('.language-selection').css('display', 'none');
    $('.chatbox').css('display', 'block');
    // Now pass the selected language to the askQuestion and fetchSuggestions functions
    $("#c_loader").hide();
    askQuestion(language);
    //fetchSuggestions(language);
  });
  // /************************/close-chatbot*******
  $('.close .fa-times').click(function() {
    $('.chatbox').css('display', 'none');
    //$("#chat-messages").remove();
    $("#chat-messages").children().not("#default_suggestion").remove();
    enabledSendSection();
  });

  function askQuestion(language) {

    $("#c_loader").show();
    //console.log("hi");
    console.log("askQuestion= " + $("#question").val());
    if (!$("#question").val()) {
      $("#c_loader").hide();
    }
    //$("#chat-loader").show();
    if (typeof language !== 'undefined') {
      selectedLanguage = language;
    }
    console.log("language=" + selectedLanguage);
    qu = $("#question").val().trim();
    if (qu) {
      //console.log("ask Question");
      const question = $("#question").val();
      const language = $("#lang").val() || "english";
      console.log(question);
      //const ques = question.toLowerCase();
      const ques = question;
      ///////////////////////Set question to the chat box panel/////////////////
      if (ques == "hello") {
        addMessageToChatbox(ques, "visitor");
      } else if (ques == "hi") {
        console.log("hi");
        addMessageToChatbox(ques, "visitor");
      }
      /*else if (ques == "form") {
             //alert(1);
             addFormToChatbox(ques,"visitor");
             //console.log("form");
             //addMessageToChatbox(ques, "visitor");
           }*/
      else {
        addMessageToChatbox(ques, "visitor");
      }
      /////////////////////////Set answer to chat box panel/////////////////////////////////////////////////
      $.ajax({
        //url: "<?php //echo base_url('SanlaapBotQuestionAnswer'); 
                ?>", // 
        url: "<?php echo base_url('aiSanlaapBotQuestionAnswerEODB'); ?>", // 
        type: "POST",
        dataType: "json",
        /*contentType: "application/json",*/
        data: {
          question: question,
          language: selectedLanguage
        },
        success: function(data) {
          var response_type=data.response_type;
          var intent_id=data.intent_id;
          var response=data.response;
          var option_list=data.option_list;
          var session_id=data.session_id;
          var resDataId = intent_id;
          //console.log("response_type="+response_type);
          console.log("data answer=" + data.response_type);
          console.log("response_type="+data.response_type);
          console.log("Response data as JSON:", JSON.stringify(data));
          var resDataId = data.id;
          console.log("id=" + data.id);

          /*const lowerCaseQuestion = (data.questions || "").toLowerCase();
          const ques = question.toLowerCase();*/
          //console.log("ques=" + ques);
          //var resDataId = 5;
          setTimeout(function() {
            //used demo condition to open the form
            /*if (data.id != "" && data.id == 2) {
              addFormToChatbox(data.response, "operator", resDataId);
            } else if (data.id != "" && data.id == 3) {
              addAadhaarFormToChatbox(data.response, "operator", resDataId);
            }*/ 
            if(response_type=="form"){
              addFormToChatbox(data, "operator", resDataId);
            }
            else if(response_type=="options"){
              addOptionMessageToChatbox(data, "operator", resDataId);

            }
            else {
              addMessageToChatbox(data.response, "operator", resDataId);
              //addOptionMessageToChatbox(data, "operator", resDataId);

            }
            $("#c_loader").hide();
          }, 500); // 2000 milliseconds = 2 seconds

        },
        error: function(error) {
          console.error("Error:", error);
          addMessageToChatbox("An error occurred.", "operator");
        }
      });
      $("#question").val("");
      $(".suggestions").empty();
    }
  }


  ///////////////////////////////////// AISanlaap /////////////////////////////////

  //options set 

  function addOptionMessageToChatbox(message, sender, dataId) {

    disabledSendSection();
    //23-6-25
    if(dataId==008){
      enabledSendSection();
    }
    else if(dataId==001){
      enabledSendSection();
    }
    else if(message.intent_id==122){
      enabledSendSection();
    }
    //const dataObj = JSON.parse(data);
    data = JSON.stringify(message)

    console.log("from addOptionMessageToChatbox");
    console.log("message" + message);
    console.log("message data=" + data);
    console.log("message data intent_id=" + message.intent_id);
    console.log("message session_id=" + message.session_id);
    console.log(message.option_list);
    console.log("length=" + message.option_list.length);
    console.log("criteria_list=" + message.criteria_list);

    /*for (var i = 0; i < message.option_list.length; i++) {

    }*/
    // Start with the form_div structure
    let form_div = `
      <div id="form_div" class="default_suggestion chat-form">
        <div class="form-group">
          <label>${message.response}</label>
        </div>   
      </div>
    `;

    // Add the buttons (if any) to the form_div
    if (message.option_list && message.option_list.length > 0) {
      let buttonHTML = message.option_list.join(''); // Join the array of button HTML strings
      console.log("buttonHTML=" + buttonHTML);
      form_div += `<div class="options-container">${buttonHTML}</div>`; // Append buttons inside a wrapper div
    }

    /*var form_div = `<div id="form_div" class="default_suggestion chat-form">
    
      <div class="form-group">
        <label>` + message.response + `</label>
      </div>   
  </div>`;*/
    scrollToBottom();
    console.log("chatbox data id is=" + dataId);
    const chatboxMessages = $(".chatbox__messages");
    // Create the outer wrapper div
    const outerDiv = $("<div>").addClass("messages__wrapper");
    // Create the message item div
    const messageItem = $("<div>")
      .addClass("messages__item")
      .addClass(`messages__item--${sender}`)
      .html(form_div);

    // Append the message item to the outer wrapper
    if (sender === "operator") {
      const likeButton = $("<button>")
        .addClass("like-btn")
        .html('<i class="fa-solid fa-thumbs-up like"></i>')
        .attr("data-id", dataId)
        .on("click", function() {
          openPopup("Liked!", dataId);
        });
      const dislikeButton = $("<button>")
        .addClass("dislike-btn")
        .html('<i class="fa-solid fa-thumbs-down dislike"></i>')
        .on("click", function() {
          openPopupD("Disliked!");
        });
      const reactionsContainer = $("<div>")
        .addClass("reactions-container")
        .append(likeButton)
        .append(dislikeButton);
      messageItem.append(reactionsContainer);
    }
    outerDiv.append(messageItem);

    chatboxMessages.append(outerDiv);
  }
  ///////////////////////////////////Add form to chat box/////////////////////////
  var count=0;
  function addFormToChatbox(message, sender, dataId) {
    disabledSendSection();
    console.log("from form box");
    count+=1;
    var form_div = ` 
        <div id="form_div${count}" class="default_suggestion chat-form">
        
          <div class="form-group">
            <label>${message.response}</label>
          </div>
          <div class="form-group">
            <label>DOB</label>
            <input type="date" class="form-control date${count}" name="dob" id="dob${count}" placeholder="Enter DOB">
          </div>
          <div class="form-group">
            <label>Gender</label>
            <select class="form-control gender${count}" name="gender" id="gender${count}">
              <option value="">Please Select</option>
              <option value="Male">Male</option>
              <option value="Female">Female</option>
              <option value="Other">Other</option>
            </select>
          </div>
          <div class="form-group">
            <label>Social Category</label>
            <select class="form-control social_category${count}" name="social_category" id="social_category${count}">
              <option value="">Please Select</option>
              <option value="General">General</option>
              <option value="SC">SC</option>
              <option value="ST">ST</option>
              <option value="OBC">OBC</option>
            </select>
          </div>
          <div class="form-group">
            <label>Marital Status</label>
            <select class="form-control marital_status${count}" name="marital_status" id="marital_status${count}">
              <option value="">Please Select</option>
              <option value="Unmarried">Unmarried</option>
              <option value="Married">Married</option>
              <option value="Widow">Widow</option>
            </select>
          </div>
          <div class="form-group">
            <label>Annual Income</label>
            <input type='number' class="form-control income${count}" name="income" id="income${count}"placeholder="Enter Annual income">
          </div>
          
          <div class="form-group text-center mt-4">
            <button class="btn btn-primary btn-sm" id="form_submit" name="form_submit"data-form-id=${count}>Submit</button>
          </div>
        
      </div>`;
    scrollToBottom();
    console.log("chatbox data id is=" + dataId);
    const chatboxMessages = $(".chatbox__messages");
    // Create the outer wrapper div
    const outerDiv = $("<div>").addClass("messages__wrapper");
    // Create the message item div
    const messageItem = $("<div>")
      .addClass("messages__item")
      .addClass(`messages__item--${sender}`)
      .html(form_div);

    

    // Append the message item to the outer wrapper
    if (sender === "operator") {
      const likeButton = $("<button>")
        .addClass("like-btn")
        .html('<i class="fa-solid fa-thumbs-up like"></i>')
        .attr("data-id", dataId)
        .on("click", function() {
          openPopup("Liked!", dataId);
        });
      const dislikeButton = $("<button>")
        .addClass("dislike-btn")
        .html('<i class="fa-solid fa-thumbs-down dislike"></i>')
        .on("click", function() {
          openPopupD("Disliked!");
        });
      const reactionsContainer = $("<div>")
        .addClass("reactions-container")
        .append(likeButton)
        .append(dislikeButton);
      messageItem.append(reactionsContainer);
    }
    outerDiv.append(messageItem);

    chatboxMessages.append(outerDiv);
  }

  //////////////////////////////////////Add Aadhaar From to Chat Box///////////////////////////////

  function disabledSendSection() {
    $("#question").prop("disabled", true);
    $("#send_button").prop("disabled", true);
  }

  function enabledSendSection() {
    $("#question").prop("disabled", false);
    $("#send_button").prop("disabled", false);
  }

  function addAadhaarFormToChatbox(message, sender, dataId) {

    console.log("from form box");
    //$('#form_div').css('display', 'block');
    //$('#form_div').removeAttr('hidden').show();

    disabledSendSection();

    var form_div = `<div id="aadhaar_form_div" class="default_suggestion chat-form">
    
      <div class="form-group">
        <label>Aadhaar Number</label>
        <input type="number" class="form-control" name="aadhaar_number" id="aadhaar_number" placeholder="Enter Aadhaar number" max="12">
      </div>
      <div class="form-group text-center mt-4">
        <button class="btn btn-primary btn-sm" id="aadhaar_form_submit" name="aadhaar_form_submit" >Submit</button>
      </div>
      
    
  </div>`;
    scrollToBottom();
    console.log("chatbox data id is=" + dataId);
    const chatboxMessages = $(".chatbox__messages");
    // Create the outer wrapper div
    const outerDiv = $("<div>").addClass("messages__wrapper");
    // Create the message item div
    const messageItem = $("<div>")
      .addClass("messages__item")
      .addClass(`messages__item--${sender}`)
      .html(form_div);
    // Append the message item to the outer wrapper
    if (sender === "operator") {
      const likeButton = $("<button>")
        .addClass("like-btn")
        .html('<i class="fa-solid fa-thumbs-up like"></i>')
        .attr("data-id", dataId)
        .on("click", function() {
          openPopup("Liked!", dataId);
        });
      const dislikeButton = $("<button>")
        .addClass("dislike-btn")
        .html('<i class="fa-solid fa-thumbs-down dislike"></i>')
        .on("click", function() {
          openPopupD("Disliked!");
        });
      const reactionsContainer = $("<div>")
        .addClass("reactions-container")
        .append(likeButton)
        .append(dislikeButton);
      messageItem.append(reactionsContainer);
    }
    outerDiv.append(messageItem);

    chatboxMessages.append(outerDiv);
  }

  function addMessageToChatbox(message, sender, dataId) {
    scrollToBottom();
    console.log("chatbox data id is=" + dataId);
    const chatboxMessages = $(".chatbox__messages");
    // Create the outer wrapper div
    const outerDiv = $("<div>").addClass("messages__wrapper");
    // Create the message item div
    const messageItem = $("<div>")
      .addClass("messages__item")
      .addClass(`messages__item--${sender}`)
      .html(message);
    // Append the message item to the outer wrapper
    if (sender === "operator") {
      const likeButton = $("<button>")
        .addClass("like-btn")
        .html('<i class="fa-solid fa-thumbs-up like"></i>')
        .attr("data-id", dataId)
        .on("click", function() {
          openPopup("Liked!", dataId);
        });
      const dislikeButton = $("<button>")
        .addClass("dislike-btn")
        .html('<i class="fa-solid fa-thumbs-down dislike"></i>')
        .on("click", function() {
          openPopupD("Disliked!");
        });
      const reactionsContainer = $("<div>")
        .addClass("reactions-container")
        .append(likeButton)
        .append(dislikeButton);
      messageItem.append(reactionsContainer);
    }
    outerDiv.append(messageItem);

    chatboxMessages.append(outerDiv);
  }

  function openPopup(reaction, dataId) {
    const modal = $("#reactionModal");
    const reactionMessage = $("#reactionMessage");
    $("#feedback").val('');
    // Store the data-id in the modal as a data attribute
    $("#dataR").val(dataId);
    //alert($("#dataR").val());
    modal.data("data-id", dataId);
    // Set the message for the popup
    reactionMessage.text(`You clicked: ${reaction}`);

    // Show the modal
    modal.css("display", "block");
  }
  $(".close-btn").on("click", function() {
    count=0;
    const modal = $("#reactionModal");
    modal.css("display", "none");
  });

  function openPopupD(reaction) {
    const modal = $("#reactionModalD");
    modal.css("display", "block");
    setTimeout(function() {
      modal.css("display", "none");
    }, 3000);
  }
  $(".close-btn").on("click", function() {
    const modal = $("#reactionModalD");
    modal.css("display", "none");
  });
  // Function to close the popup modal
  $(window).on("click", function(event) {
    const modal = $("#reactionModal");
    if ($(event.target).is(modal)) {
      modal.css("display", "none");
    }
  });
  $(window).on("click", function(event) {
    const modal = $("#reactionModalD");
    if ($(event.target).is(modal)) {
      modal.css("display", "none");
    }
  });
  let loaderTimeout;

  function addLoader(parentDiv) {
    const loaderItem = $("<div>")
      .addClass("messages__item messages__item--loader")
      .html('<span class="dot"></span><span class="dot"></span><span class="dot"></span>');
    parentDiv.append(loaderItem);
    return loaderItem;
  }

  function removeLoader(loaderItem) {
    clearTimeout(loaderTimeout);
    loaderItem.remove();
  }

  /*function scrollToBottom() {
    const chatboxMessages = $(".chatbox__messages");
    console.log(chatboxMessages[0].scrollHeight); // Log the scroll height
    chatboxMessages.scrollTop(chatboxMessages[0].scrollHeight);
  }*/
  //25-04-25

  function scrollToBottom() {
    const chatboxMessages = $(".chatbox__messages");
    console.log("Initial scrollHeight:", chatboxMessages[0].scrollHeight);

    // Check if scrollHeight updates after content is added
    setTimeout(() => {
      console.log("After content addition - scrollHeight: ", chatboxMessages[0].scrollHeight);
      chatboxMessages.scrollTop(chatboxMessages[0].scrollHeight);
    }, 1); // Delay in case of content loading
  }

  let isScrolledToBottom = true;

  function checkStatus() {
    const applicant_id = $("#application_id").val();
    const year = $("#year").val();
    if ($.trim(applicant_id) === '' || $.trim(year) === '') {
      $("#result").html("<p>Please enter both Application ID and Year.</p>");
      return;
    }
    $.ajax({
      url: "/controller/status_method", // Replace with your CodeIgniter route for status checking
      type: "POST",
      dataType: "json",
      contentType: "application/json",
      data: JSON.stringify({
        applicant_id: applicant_id,
        year: year
      }),
      success: function(data) {
        $("#result").html(`<p>Status: ${data.status}</p>`);
      },
      error: function(error) {
        console.error("Error:", error);
        $("#result").html("<p>An error occurred.</p>");
      }
    });
  }

  function selectQuestion(question) {
    // Disable all main menu options
    $('#apply_licence').addClass('disabled').off('click');
    $('#know_status').addClass('disabled').off('click');
    $('#raise_query').addClass('disabled').off('click');
    $('#faq').addClass('disabled').off('click');

    // Set the selected question in the input field
    $("#question").val(question);
    // Automatically call the askQuestion function
    askQuestion();
  }

  function showComingSoon(option) {
    // Show a coming soon message for disabled options
    addMessageToChatbox("This feature is coming soon. Please use the text box below to ask your queries.", "operator");
  }

  function reactionSubmit() {
    dataid = $("#dataR").val();

    console.log($("#feedback").val());
    var feedback = $("#feedback").val();
    $("#reactionModal").hide();
    $.ajax({
      url: "<?php echo base_url('super_admin/ChatbotEntryController/chatFeedbackSocialRegistry'); ?>", // Replace with your CodeIgniter route for suggestions
      type: "POST",
      dataType: "json",
      /*contentType: "application/json",*/
      data: {
        feedback: feedback,
        dataid: dataid
      },
      success: function(data) {
        console.log("response=" + data)
        $("#result").html(`<p>Status: ${data.status}</p>`);
      },
      error: function(error) {
        console.error("Error:", error);
        $("#result").html("<p>An error occurred.</p>");
      }
    });
  }

  function cancel() {
    $("#reactionModal").hide();
  }

  $(document).ready(function() {

    // Use event delegation to handle the submit event
    //////////////////////////////////For from submit////////////////////////////
    $(document).on("click", "#form_submit", function(e) {
      c=$(this).data('form-id');
      //alert(c);

      isValidate = false;
      //console.log("hii");
      e.preventDefault();
      var dob = $("#dob"+c).val().trim();
      var gender = $("#gender"+c).val().trim();
      var marital_status = $("#marital_status"+c).val().trim();
      var social_category = $("#social_category"+c).val().trim();
      var income = $("#income"+c).val().trim();
      //var name = $("#name").val();
      /*var dob = $("#dob").val().trim();
      var gender = $("#gender").val().trim();
      var marital_status = $("#marital_status").val().trim();
      var social_category = $("#social_category").val().trim();
      var income = $("#income").val().trim();*/
      var ques="dob="+dob+", gender="+gender+", marital_status="+marital_status+", social_category="+social_category+", income="+income;
      addMessageToChatbox(ques, "visitor");
      /*if (name === "") {
        isValidate = false;
        $("#name_error").remove();
        $("#name").css("border-color", "red");
        $("#name").after('<span id="name_error" style="color: red; font-size: 12px;">Name Field is Required.</span>');
      }*/
      if (dob === "") {
        isValidate = false;
        $("#dob_error").remove();
        $("#dob").css("border-color", "red");
        $("#dob").after('<span id="dob_error" style="color: red; font-size: 12px;">Date of Birth Field is Required.</span>');
      }
      if (gender === "") {
        isValidate = false;
        $("#gender_error").remove();
        $("#gender").css("border-color", "red");
        $("#gender").after('<span id="gender_error" style="color: red; font-size: 12px;">Gender Field is Required.</span>');
      }
      if (marital_status === "") {
        isValidate = false;
        $("#marital_status_error").remove();
        $("#marital_status").css("border-color", "red");
        $("#marital_status").after('<span id="marital_status_error" style="color: red; font-size: 12px;">Marital Status Field is Required.</span>');
      }
      if (social_category === "") {
        isValidate = false;
        $("#social_category_error").remove();
        $("#social_category").css("border-color", "red");
        $("#social_category").after('<span id="social_category_error" style="color: red; font-size: 12px;">Social Category Field is Required.</span>');

      }
      if (income === "") {
        isValidate = false;
        $("#income_error").remove();
        $("#income").css("border-color", "red");
        $("#income").after('<span id="income_error" style="color: red; font-size: 12px;">Annual Income Field is Required.</span>');

      }


      //error check
      /*if (name != "") {
        $("#name").css("border-color", "green");
        $("#name_error").remove();
      }*/
      if (dob != "") {
        $("#dob").css("border-color", "green");
        $("#dob_error").remove();
      }
      if (gender != "") {
        $("#gender").css("border-color", "green");
        $("#gender_error").remove();
      }
      if (marital_status != "") {
        $("#marital_status").css("border-color", "green");
        $("#marital_status_error").remove();
      }
      if (social_category != "") {
        $("#social_category").css("border-color", "green");
        $("#social_category_error").remove();
      }
      if (income != "") {
        $("#income").css("border-color", "green");
        $("#income_error").remove();
      }

      //Check all the field is not empty
      if (dob != "" && gender != "" && marital_status != "" && social_category != "" && income != "") {
        isValidate = true;
      }

      if (!isValidate) {
        console.log("invalid");
        return;
      }
      else{
        console.log("valid");
      }




      // Now you can do something with these values, like sending them somewhere or displaying them.
      console.log({
        name,
        dob,
        gender,
        marital_status,
        social_category
      });


      $.ajax({
        url: "<?php echo base_url('super_admin/ChatbotEntryController/chatFormSubmitSocialRegistry'); ?>", // Replace with your CodeIgniter route for suggestions
        type: "POST",
        dataType: "json",
        /*contentType: "application/json",*/
        data: {
          dob: dob,
          gender: gender,
          marital_status: marital_status,
          social_category: social_category,
          income:income
        },
        success: function(data) {
          /*console.log("response=" + data)
          $("#result").html(`<p>Status: ${data.status}</p>`);*/

          if(data.response_type="options"){
            var resDataId=data.intent_id;
              addOptionMessageToChatbox(data, "operator", resDataId);

            }
        },
        error: function(error) {
          console.error("Error:", error);
          $("#result").html("<p>An error occurred.</p>");
        }
      });

      /*$("#dob").val("");
        $("#gender").val("");
        $("#marital_status").val("");
        $("#social_category").val("");
        $("#income").val("");*/
    });

    /////////////////////////////////For Aadhaar Form Submit///////////////////////////////////////
    



    ////////////////////////////////////////////////////AAdhaar Number Validation using Verhoeff Algorithm ////////////////// 
    // multiplication table d
    var d = [
      [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
      [1, 2, 3, 4, 0, 6, 7, 8, 9, 5],
      [2, 3, 4, 0, 1, 7, 8, 9, 5, 6],
      [3, 4, 0, 1, 2, 8, 9, 5, 6, 7],
      [4, 0, 1, 2, 3, 9, 5, 6, 7, 8],
      [5, 9, 8, 7, 6, 0, 4, 3, 2, 1],
      [6, 5, 9, 8, 7, 1, 0, 4, 3, 2],
      [7, 6, 5, 9, 8, 2, 1, 0, 4, 3],
      [8, 7, 6, 5, 9, 3, 2, 1, 0, 4],
      [9, 8, 7, 6, 5, 4, 3, 2, 1, 0]
    ];

    // permutation table p
    var p = [
      [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
      [1, 5, 7, 6, 2, 8, 3, 0, 9, 4],
      [5, 8, 0, 3, 7, 9, 6, 1, 4, 2],
      [8, 9, 1, 6, 0, 4, 3, 5, 2, 7],
      [9, 4, 5, 3, 1, 2, 6, 8, 7, 0],
      [4, 2, 8, 6, 5, 7, 3, 9, 0, 1],
      [2, 7, 9, 3, 8, 0, 6, 4, 1, 5],
      [7, 0, 4, 6, 9, 1, 3, 2, 5, 8]
    ];

    // inverse table inv
    var inv = [0, 4, 3, 2, 1, 5, 6, 7, 8, 9];

    // converts string or number to an array and inverts it
    function invArray(array) {
      if (Object.prototype.toString.call(array) == "[object Number]") {
        array = String(array);
      }

      if (Object.prototype.toString.call(array) == "[object String]") {
        array = array.split("").map(Number);
      }

      return array.reverse();
    }

    // generates checksum
    function generate(array) {
      var c = 0;
      var invertedArray = invArray(array);

      for (var i = 0; i < invertedArray.length; i++) {
        c = d[c][p[((i + 1) % 8)][invertedArray[i]]];
      }

      return inv[c];
    }

    // validates checksum
    function validate(array) {
      var c = 0;
      var invertedArray = invArray(array);

      for (var i = 0; i < invertedArray.length; i++) {
        c = d[c][p[(i % 8)][invertedArray[i]]];
      }
      return (c === 0);
    }

    // jQuery function to validate Aadhaar number
    $(document).on("keyup", "#aadhaar_number", function(e) {
      e.preventDefault();

      var aadhaar_number = $("#aadhaar_number").val().trim(); // Remove extra spaces

      // Regular Expression for Aadhaar number validation (12 digits)
      var aadhaarRegex = /^\d{12}$/;

      // Remove any previous error message
      $("#aadhaar_error").text("");

      // Check if the number matches the 12-digit format
      if (aadhaar_number.match(aadhaarRegex)) {
        console.log("Valid format for Aadhaar number");

        // Now, check using the Verhoeff algorithm for checksum validation
        if (validate(aadhaar_number)) {
          console.log("Valid Aadhaar number (Checksum validated)");
          // Optionally, visual feedback for valid input
          $("#aadhaar_number").css("border-color", "green");
          $("#aadhaar_number").after('<span id="aadhaar_error" style="color: green; font-size: 12px;">Valid Aadhaar number.</span>');

        } else {
          console.log("Invalid Aadhaar number (Checksum failed)");
          // Visual feedback for invalid input
          $("#aadhaar_number").after('<span id="aadhaar_error" style="color: red; font-size: 12px;">Invalid Aadhaar number (Checksum failed).</span>');
          $("#aadhaar_number").css("border-color", "red");
          //$("#aadhaar_error").text("Invalid Aadhaar number (Checksum failed).");
        }

      } else {
        console.log("Invalid Aadhaar number format");
        // Visual feedback for invalid input
        $("#aadhaar_number").css("border-color", "red");

        // Display error message
        $("#aadhaar_number").after('<span id="aadhaar_error" style="color: red; font-size: 12px;">Invalid Aadhaar number format. It should be exactly 12 digits.</span>');

      }
    });

    ///////////////////////////////End of Aadhaar Number Validation/////////////////////////////

    //session create autometically when chat box open and select lenguage 
    $(document).on("click", "#select_L_B", function() {
      //alert("selected language");

      $.ajax({
        url: "<?php echo base_url('super_admin/ChatbotEntryController/sessionCreateEODB'); ?>", // Replace with your CodeIgniter route for suggestions
        type: "POST",
        dataType: "json",
        /*contentType: "application/json",*/
        data: {
          type: "session"

        },
        success: function(data) {
          console.log("response=" + data)
          $("#result").html(`<p>Status: ${data.status}</p>`);
        },
        error: function(error) {
          console.error("Error:", error);
          $("#result").html("<p>An error occurred.</p>");
        }
      });


    });



    $(document).on("click", ".close", function() {
      //alert("selected language");
      //alert("hello");

      $.ajax({
        url: "<?php echo base_url('super_admin/ChatbotEntryController/sessionDestroyEODB'); ?>", // Replace with your CodeIgniter route for suggestions
        type: "POST",
        dataType: "json",
        /*contentType: "application/json",*/
        data: {
          type: "session"

        },
        success: function(data) {
          console.log("response=" + data)
          $("#result").html(`<p>Status: ${data.status}</p>`);
        },
        error: function(error) {
          console.error("Error:", error);
          $("#result").html("<p>An error occurred.</p>");
        }
      });


    });

    /*$(document).on("click", "#yes_btn", function() {
      var v = $("#yes_btn").val();
      //alert("selected language");
      alert("hello");
      alert(v);

    });*/

    // Attach click event handler to all buttons with class 'option_btn'
    $(document).on("click", ".option_btn", function() {
      // Get the value of the clicked button
      var buttonValue = $(this).val();
      console.log("Button clicked with value:", buttonValue);
      var ques=$(this).text();
      addMessageToChatbox(ques, "visitor");

      // You can now use the buttonValue for further logic
      // Example: perform some action based on the clicked value
      /*if (buttonValue === "student") {
        console.log("User selected student");
      } else if (buttonValue === "women") {
        console.log("User selected women");
      } else if (buttonValue === "farmer") {
        console.log("User selected farmer");
      } else if (buttonValue === "other") {
        console.log("User selected other");
      }*/

      $.ajax({
        url: "<?php echo base_url('aiSanlaapBotQuestionAnswerEODB'); ?>", // Replace with your CodeIgniter route for suggestions
        type: "POST",
        dataType: "json",
        /*contentType: "application/json",*/
        data: {
          /*scheme_bucket:buttonValue*/
          question: buttonValue
        },
        success: function(data) {
          /*console.log("response=" + data)
          $("#result").html(`<p>Status: ${data.status}</p>`);*/
          if(data=="done"){
            alert("Process Complete");
          }

          if(data.response_type=="options"){
            console.log("options type is selected");
            console.log("step="+data.step);
            var step=data.step;
            //alert("options");
            var resDataId=data.intent_id;
              addOptionMessageToChatbox(data, "operator", resDataId);

            }
            else if(data.response_type=="text"){
              str = data.response.replace(/\s+/g, ' ').trim();
              addMessageToChatbox(str, "operator", resDataId);
              //addMessageToChatbox(data.response, "operator", resDataId);
              enabledSendSection();
              //alert(data.response)
              //To enable check my eligibility button 
              $('#eligibility').removeClass('disabled'); 
              $('#eligibility').on('click');
            }
        },
        error: function(error) {
          console.error("Error:", error);
          $("#result").html("<p>An error occurred.</p>");
        }
      });
    });





  });

  //session destroy autometically on page load
  $(document).ready(function() {

    $.ajax({
      url: "<?php echo base_url('super_admin/ChatbotEntryController/sessionDestroyEODB'); ?>", // Replace with your CodeIgniter route for suggestions
      type: "POST",
      dataType: "json",
      /*contentType: "application/json",*/
      data: {
        type: "session"

      },
      success: function(data) {
        console.log("response=" + data)
        $("#result").html(`<p>Status: ${data.status}</p>`);
      },
      error: function(error) {
        console.error("Error:", error);
        $("#result").html("<p>An error occurred.</p>");
      }
    });



  });

  //12-6-25
  function sendMessage(previous_menu){
    //alert("hello");
    var ques=$(this).text();
      addMessageToChatbox(previous_menu, "visitor");

      var buttonValue=previous_menu;

      $.ajax({
        url: "<?php echo base_url('aiSanlaapBotQuestionAnswerEODB'); ?>", // Replace with your CodeIgniter route for suggestions
        type: "POST",
        dataType: "json",
        /*contentType: "application/json",*/
        data: {
          /*scheme_bucket:buttonValue*/
          question: buttonValue
        },
        success: function(data) {
          /*console.log("response=" + data)
          $("#result").html(`<p>Status: ${data.status}</p>`);*/
          if(data=="done"){
            alert("Process Complete");
          }

          if(data.response_type=="options"){
            console.log("options type is selected");
            console.log("step="+data.step);
            var step=data.step;
            //alert("options");
            var resDataId=data.intent_id;
              addOptionMessageToChatbox(data, "operator", resDataId);

            }
            else if(data.response_type=="text"){
              str = data.response.replace(/\s+/g, ' ').trim();
              addMessageToChatbox(str, "operator", resDataId);
              //addMessageToChatbox(data.response, "operator", resDataId);
              enabledSendSection();
              //alert(data.response)
              //To enable check my eligibility button 
              $('#eligibility').removeClass('disabled'); 
              $('#eligibility').on('click');
            }
        },
        error: function(error) {
          console.error("Error:", error);
          $("#result").html("<p>An error occurred.</p>");
        }
      });
    

  }

</script>





