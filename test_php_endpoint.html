<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test PHP Endpoint</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ccc;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .response {
            background: #f0f0f0;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .error {
            color: red;
            background: #ffe6e6;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            color: green;
            background: #e6ffe6;
            padding: 10px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>Test PHP Endpoint</h1>
    
    <div class="test-section">
        <h3>Test PHP Endpoint Accessibility</h3>
        <p>This will test if the PHP endpoint 'aiSanlaapBotQuestionAnswerEODB' is accessible.</p>
        <button onclick="testPHPEndpoint()">Test PHP Endpoint</button>
        <div id="php-test-response" class="response"></div>
    </div>

    <div class="test-section">
        <h3>Test with Different Questions</h3>
        <button onclick="testQuestion('Choose sector or industry')">Test: Choose sector or industry</button>
        <button onclick="testQuestion('1. Apply for licence/clearance')">Test: Apply for licence/clearance</button>
        <button onclick="testQuestion('2. Know application status')">Test: Know application status</button>
        <div id="question-test-response" class="response"></div>
    </div>

    <div class="test-section">
        <h3>Debug Information</h3>
        <button onclick="getDebugInfo()">Get Debug Info</button>
        <div id="debug-response" class="response"></div>
    </div>

    <script>
        function testPHPEndpoint() {
            $('#php-test-response').html('Testing PHP endpoint...');
            
            $.ajax({
                url: 'aiSanlaapBotQuestionAnswerEODB',
                type: 'POST',
                dataType: 'json',
                data: {
                    question: 'test',
                    language: '1'
                },
                success: function(data) {
                    $('#php-test-response').html('<div class="success">PHP Endpoint is accessible!</div>' + JSON.stringify(data, null, 2));
                },
                error: function(xhr, status, error) {
                    $('#php-test-response').html('<div class="error">PHP Endpoint Error:<br>' +
                        'Status: ' + status + '<br>' +
                        'Error: ' + error + '<br>' +
                        'HTTP Status: ' + xhr.status + '<br>' +
                        'Response Text: ' + xhr.responseText + '</div>');
                }
            });
        }

        function testQuestion(question) {
            $('#question-test-response').html('Testing question: ' + question + '...');
            
            $.ajax({
                url: 'aiSanlaapBotQuestionAnswerEODB',
                type: 'POST',
                dataType: 'json',
                data: {
                    question: question,
                    language: '1'
                },
                success: function(data) {
                    $('#question-test-response').html('<div class="success">Success for: ' + question + '</div>' + JSON.stringify(data, null, 2));
                },
                error: function(xhr, status, error) {
                    $('#question-test-response').html('<div class="error">Error for: ' + question + '<br>' +
                        'Status: ' + status + '<br>' +
                        'Error: ' + error + '<br>' +
                        'HTTP Status: ' + xhr.status + '<br>' +
                        'Response Text: ' + xhr.responseText + '</div>');
                }
            });
        }

        function getDebugInfo() {
            $('#debug-response').html('Getting debug information...');
            
            var debugInfo = {
                'Current URL': window.location.href,
                'User Agent': navigator.userAgent,
                'jQuery Version': $.fn.jquery,
                'Base URL Test': 'aiSanlaapBotQuestionAnswerEODB',
                'Timestamp': new Date().toISOString()
            };
            
            $('#debug-response').html('<div class="success">Debug Information:</div>' + JSON.stringify(debugInfo, null, 2));
        }
    </script>
</body>
</html>
