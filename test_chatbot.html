<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test EoDB Chatbot</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ccc;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .response {
            background: #f0f0f0;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            white-space: pre-wrap;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .error {
            color: red;
            background: #ffe6e6;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            color: green;
            background: #e6ffe6;
            padding: 10px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>EoDB Chatbot Test</h1>
    
    <div class="test-section">
        <h3>Test 1: Direct FastAPI Connection</h3>
        <button onclick="testDirectAPI()">Test FastAPI Directly</button>
        <div id="direct-response" class="response"></div>
    </div>

    <div class="test-section">
        <h3>Test 2: PHP Backend Connection</h3>
        <button onclick="testPHPBackend()">Test PHP Backend</button>
        <div id="php-response" class="response"></div>
    </div>

    <div class="test-section">
        <h3>Test 3: Simulate Frontend Flow</h3>
        <button onclick="simulateOption1()">Click Option 1: Apply for licence/clearance</button>
        <button onclick="simulateOption2()">Click Option 2: Know application status</button>
        <div id="simulate-response" class="response"></div>
    </div>

    <script>
        function testDirectAPI() {
            $('#direct-response').html('Testing...');
            
            $.ajax({
                url: 'http://127.0.0.1:8020/chatbot/step',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    "session_id": "test-session",
                    "collection_name": "collection_93423009_379f_4a90_99ac_cc42ca875f57",
                    "user_input": "Choose sector or industry",
                    "step": 1,
                    "user_response": {
                        "caption": "",
                        "value": ""
                    },
                    "response_type": "",
                    "followup_yes": "",
                    "followup_no": "",
                    "criteria_list": []
                }),
                success: function(data) {
                    $('#direct-response').html('<div class="success">Success!</div>' + JSON.stringify(data, null, 2));
                },
                error: function(xhr, status, error) {
                    $('#direct-response').html('<div class="error">Error: ' + error + '<br>Status: ' + status + '<br>Response: ' + xhr.responseText + '</div>');
                }
            });
        }

        function testPHPBackend() {
            $('#php-response').html('Testing...');
            
            $.ajax({
                url: 'aiSanlaapBotQuestionAnswerEODB',
                type: 'POST',
                dataType: 'json',
                data: {
                    question: 'Choose sector or industry',
                    language: '1'
                },
                success: function(data) {
                    $('#php-response').html('<div class="success">Success!</div>' + JSON.stringify(data, null, 2));
                },
                error: function(xhr, status, error) {
                    $('#php-response').html('<div class="error">Error: ' + error + '<br>Status: ' + status + '<br>Response: ' + xhr.responseText + '</div>');
                }
            });
        }

        function simulateOption1() {
            $('#simulate-response').html('Testing Option 1...');
            
            $.ajax({
                url: 'aiSanlaapBotQuestionAnswerEODB',
                type: 'POST',
                dataType: 'json',
                data: {
                    question: '1. Apply for licence/clearance',
                    language: '1'
                },
                success: function(data) {
                    $('#simulate-response').html('<div class="success">Success!</div>' + JSON.stringify(data, null, 2));
                },
                error: function(xhr, status, error) {
                    $('#simulate-response').html('<div class="error">Error: ' + error + '<br>Status: ' + status + '<br>Response: ' + xhr.responseText + '</div>');
                }
            });
        }

        function simulateOption2() {
            $('#simulate-response').html('Testing Option 2...');
            
            $.ajax({
                url: 'aiSanlaapBotQuestionAnswerEODB',
                type: 'POST',
                dataType: 'json',
                data: {
                    question: '2. Know application status',
                    language: '1'
                },
                success: function(data) {
                    $('#simulate-response').html('<div class="success">Success!</div>' + JSON.stringify(data, null, 2));
                },
                error: function(xhr, status, error) {
                    $('#simulate-response').html('<div class="error">Error: ' + error + '<br>Status: ' + status + '<br>Response: ' + xhr.responseText + '</div>');
                }
            });
        }
    </script>
</body>
</html>
