<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EoDB Chatbot Test</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .chat-container {
            height: 500px;
            overflow-y: auto;
            padding: 20px;
            border-bottom: 1px solid #eee;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px 15px;
            border-radius: 20px;
            max-width: 70%;
            word-wrap: break-word;
        }
        .message.user {
            background: #007bff;
            color: white;
            margin-left: auto;
            text-align: right;
        }
        .message.bot {
            background: #f1f1f1;
            color: #333;
        }
        .suggestion-item {
            border-radius: 20px;
            text-align: left;
            overflow-wrap: break-word;
            background: linear-gradient(65deg, #33b1be80, #32c5c01a) !important;
            color: #333;
            border: 1px solid #ddd;
            padding: 10px 15px;
            margin: 5px 0;
            font-size: 12px;
            font-weight: 500;
            display: flex;
            align-items: center;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .suggestion-item:hover {
            background-color: #d4d4d4 !important;
        }
        .suggestion-item.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            background: #f0f0f0 !important;
            color: #999 !important;
        }
        .suggestion-item.disabled:hover {
            background: #f0f0f0 !important;
        }
        .input-container {
            padding: 20px;
            border-top: 1px solid #eee;
        }
        .input-group {
            display: flex;
            gap: 10px;
        }
        #question {
            flex: 1;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 25px;
            outline: none;
            font-size: 14px;
        }
        #send-btn {
            padding: 12px 20px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
        }
        #send-btn:hover {
            background: #0056b3;
        }
        .loader {
            display: none;
            text-align: center;
            padding: 10px;
        }
        .dot {
            width: 8px;
            height: 8px;
            background-color: #007bff;
            border-radius: 50%;
            display: inline-block;
            margin: 0 2px;
            animation: bounce 1.5s infinite ease-in-out;
        }
        .dot:nth-child(2) { animation-delay: 0.3s; }
        .dot:nth-child(3) { animation-delay: 0.6s; }
        @keyframes bounce {
            0%, 80%, 100% { transform: scale(0); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }
        .config-section {
            padding: 20px;
            background: #f8f9fa;
            border-top: 1px solid #eee;
        }
        .config-group {
            margin-bottom: 15px;
        }
        .config-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .config-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-buttons {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }
        .test-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        .test-btn.primary { background: #007bff; color: white; }
        .test-btn.secondary { background: #6c757d; color: white; }
        .test-btn.success { background: #28a745; color: white; }
        .test-btn.danger { background: #dc3545; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>EoDB Chatbot Test Interface</h1>
            <p>Test the updated chatbot functionality</p>
        </div>
        
        <div class="chat-container" id="chat-messages">
            <div id="default_suggestion" class="default_suggestion">
                <!-- Dynamic content will be loaded here -->
            </div>
            
            <div class="loader" id="c_loader">
                <div class="dot"></div>
                <div class="dot"></div>
                <div class="dot"></div>
            </div>
        </div>
        
        <div class="input-container">
            <div class="input-group">
                <input type="text" id="question" placeholder="Type your message here..." onkeypress="handleKeyPress(event)">
                <button id="send-btn" onclick="askQuestion()">Send</button>
            </div>
        </div>
        
        <div class="config-section">
            <h3>Configuration</h3>
            <div class="config-group">
                <label for="api-url">API URL:</label>
                <input type="text" id="api-url" value="http://localhost:8000/chatbot" placeholder="Enter your FastAPI server URL">
            </div>
            <div class="config-group">
                <label for="session-id">Session ID:</label>
                <input type="text" id="session-id" value="" placeholder="Auto-generated session ID">
            </div>
            <div class="test-buttons">
                <button class="test-btn primary" onclick="testLicenceFlow()">Test Licence Flow</button>
                <button class="test-btn secondary" onclick="testStatusFlow()">Test Status Flow</button>
                <button class="test-btn success" onclick="testKeywords()">Test Keywords</button>
                <button class="test-btn danger" onclick="clearChat()">Clear Chat</button>
            </div>
        </div>
    </div>

    <script>
        // Generate session ID
        let sessionId = 'test_' + Math.random().toString(36).substr(2, 9);
        document.getElementById('session-id').value = sessionId;

        // Global config object
        let chatbotConfig = null;
        
        function generateSessionId() {
            return 'test_' + Math.random().toString(36).substr(2, 9);
        }

        async function loadChatbotConfig() {
            try {
                const apiUrl = $("#api-url").val() || "http://localhost:8000";
                const response = await fetch(`${apiUrl}/chatbot/config`);
                const data = await response.json();

                if (data.success) {
                    chatbotConfig = data.config;
                    applyDynamicConfig();
                } else {
                    console.error('Failed to load config:', data.error);
                    // Use fallback config
                    useFallbackConfig();
                }
            } catch (error) {
                console.error('Error loading config:', error);
                useFallbackConfig();
            }
        }

        function useFallbackConfig() {
            chatbotConfig = {
                chatbot_name: "AI Sanlaap",
                greeting: {
                    initial_greeting: "Namaskar ! I am your Virtual Assistant for AI Sanlaap !",
                    service_help_text: "I can help you with the following services:",
                    general_query_prompt: "If you have any other queries, you may type here ........",
                    coming_soon_message: "This feature is coming soon. Please use the text box below to ask your queries."
                },
                options: {
                    apply_licence: { name: "1. Apply for licence/clearance", enabled: true, disabled_message: "" },
                    know_status: { name: "2. Know application status", enabled: true, disabled_message: "" },
                    raise_query: { name: "3. Raise any query / Grievance", enabled: false, disabled_message: "This feature is coming soon. Please use the text box below to ask your queries." },
                    faq: { name: "4. FAQ", enabled: false, disabled_message: "This feature is coming soon. Please use the text box below to ask your queries." }
                }
            };
            applyDynamicConfig();
        }

        function applyDynamicConfig() {
            if (!chatbotConfig) return;

            // Update page title
            document.title = `${chatbotConfig.chatbot_name} Test Interface`;

            // Build dynamic greeting
            const defaultSuggestion = document.getElementById('default_suggestion');
            let html = `
                <div class="message bot">
                    <p><b>${chatbotConfig.greeting.initial_greeting}</b></p>
                    <p>${chatbotConfig.greeting.service_help_text}</p>
                </div>
            `;

            // Add option buttons based on configuration
            Object.keys(chatbotConfig.options).forEach(optionKey => {
                const option = chatbotConfig.options[optionKey];
                const disabledClass = option.enabled ? '' : ' disabled';
                const clickHandler = option.enabled ?
                    `selectQuestion('${option.name}')` :
                    `showComingSoon('${option.name}', '${option.disabled_message}')`;

                html += `
                    <div class="suggestion-item${disabledClass}" onclick="${clickHandler}" id="${optionKey}">
                        ${option.name}
                    </div>
                `;
            });

            html += `
                <div class="message bot">
                    <p><b>${chatbotConfig.greeting.general_query_prompt}</b></p>
                </div>
            `;

            defaultSuggestion.innerHTML = html;
        }
        
        function addMessageToChatbox(message, sender) {
            const chatContainer = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender === 'visitor' ? 'user' : 'bot'}`;
            messageDiv.innerHTML = message;
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }
        
        function selectQuestion(question) {
            // Disable all main menu options
            $('#apply_licence').addClass('disabled').off('click');
            $('#know_status').addClass('disabled').off('click');
            $('#raise_query').addClass('disabled').off('click');
            $('#faq').addClass('disabled').off('click');
            
            // Set the selected question in the input field
            $("#question").val(question);
            // Automatically call the askQuestion function
            askQuestion();
        }
        
        function showComingSoon(option, customMessage = null) {
            const message = customMessage ||
                (chatbotConfig ? chatbotConfig.greeting.coming_soon_message :
                "This feature is coming soon. Please use the text box below to ask your queries.");
            addMessageToChatbox(message, "bot");
        }
        
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                askQuestion();
            }
        }
        
        function askQuestion() {
            const question = $("#question").val().trim();
            if (!question) return;
            
            const apiUrl = $("#api-url").val() || "http://localhost:8000/chatbot";
            sessionId = $("#session-id").val() || generateSessionId();
            
            // Show user message
            addMessageToChatbox(question, "visitor");
            
            // Clear input
            $("#question").val("");
            
            // Show loader
            $("#c_loader").show();
            
            // Make API call
            $.ajax({
                url: apiUrl,
                type: "POST",
                dataType: "json",
                data: {
                    question: question,
                    session_id: sessionId,
                    language: "english"
                },
                success: function(data) {
                    $("#c_loader").hide();
                    
                    if (data.response) {
                        addMessageToChatbox(data.response, "bot");
                    }
                    
                    if (data.option_list && data.option_list !== "NA") {
                        const optionsDiv = document.createElement('div');
                        optionsDiv.innerHTML = data.option_list;
                        document.getElementById('chat-messages').appendChild(optionsDiv);
                    }
                },
                error: function(xhr, status, error) {
                    $("#c_loader").hide();
                    addMessageToChatbox("Error: " + error + ". Please check if the FastAPI server is running.", "bot");
                }
            });
        }
        
        function testLicenceFlow() {
            $("#question").val("licence");
            askQuestion();
        }
        
        function testStatusFlow() {
            $("#question").val("application status");
            askQuestion();
        }
        
        function testKeywords() {
            addMessageToChatbox("Testing keywords: Try typing 'exit', 'restart', 'licence', 'clearance', or 'application status'", "bot");
        }
        
        function clearChat() {
            const chatContainer = document.getElementById('chat-messages');
            chatContainer.innerHTML = `
                <div id="default_suggestion" class="default_suggestion">
                    <div class="message bot">
                        <p><b>Namaskar ! I am your Virtual Assistant for AI Sanlaap !</b></p>
                        <p>I can help you with the following services:</p>
                    </div>
                    <div class="suggestion-item" onclick="selectQuestion('1. Apply for licence/clearance')" id="apply_licence">
                        1. Apply for licence/clearance
                    </div>
                    <div class="suggestion-item" onclick="selectQuestion('2. Know application status')" id="know_status">
                        2. Know application status
                    </div>
                    <div class="suggestion-item disabled" onclick="showComingSoon('3. Raise any query / Grievance')" id="raise_query">
                        3. Raise any query / Grievance
                    </div>
                    <div class="suggestion-item disabled" onclick="showComingSoon('4. FAQ')" id="faq">
                        4. FAQ
                    </div>
                    <div class="message bot">
                        <p><b>If you have any other queries, you may type here ........</b></p>
                    </div>
                </div>
            `;
            sessionId = generateSessionId();
            document.getElementById('session-id').value = sessionId;
        }

        // Initialize the chatbot when page loads
        $(document).ready(function() {
            loadChatbotConfig();
        });
    </script>
</body>
</html>
