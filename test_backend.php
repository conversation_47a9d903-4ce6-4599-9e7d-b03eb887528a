<?php
// Simple test script to check the backend connection
header('Content-Type: application/json');

// Test data
$session_id = "test-session-" . time();
$user_input = "Choose sector or industry";
$next_step = 1;
$caption = "";
$value = "";

// Prepare the request data
$postData = json_encode([
    "session_id" => $session_id,
    "collection_name" => "collection_93423009_379f_4a90_99ac_cc42ca875f57",
    "user_input" => $user_input,
    "step" => $next_step,
    "user_response" => [
        "caption" => $caption,
        "value" => $value
    ],
    "response_type" => "",
    "followup_yes" => "",
    "followup_no" => "",
    "criteria_list" => []
]);

// Initialize cURL
$curl = curl_init();

curl_setopt_array($curl, array(
    CURLOPT_URL => 'http://127.0.0.1:8020/chatbot/step',
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_ENCODING => '',
    CURLOPT_MAXREDIRS => 10,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_FOLLOWLOCATION => true,
    CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
    CURLOPT_CUSTOMREQUEST => 'POST',
    CURLOPT_POSTFIELDS => $postData,
    CURLOPT_HTTPHEADER => array(
        'Content-Type: application/json'
    ),
));

$response = curl_exec($curl);
$http_code = curl_getinfo($curl, CURLINFO_HTTP_CODE);
$curl_error = curl_error($curl);

curl_close($curl);

// Prepare response
$result = [
    'test_status' => 'completed',
    'request_data' => json_decode($postData, true),
    'http_code' => $http_code,
    'curl_error' => $curl_error,
    'response' => $response ? json_decode($response, true) : null,
    'raw_response' => $response
];

echo json_encode($result, JSON_PRETTY_PRINT);
?>
