#!/usr/bin/env python3
"""
Test script to simulate the complete EoDB chatbot flow
"""

import requests
import json
import time

def test_session_creation():
    """Test session creation"""
    print("🔄 Testing session creation...")
    
    url = 'http://127.0.0.1:8020/session_manager'
    data = {
        'session_type': 'session_create',
        'session_id': ''
    }
    
    try:
        response = requests.post(url, json=data, timeout=10)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Session created: {result['session_id']}")
            return result['session_id']
        else:
            print(f"❌ Session creation failed: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ Session creation error: {e}")
        return None

def test_chatbot_step(session_id, user_input, step=1, caption="", value=""):
    """Test chatbot step"""
    print(f"🔄 Testing chatbot step {step} with input: '{user_input}'")
    
    url = 'http://127.0.0.1:8020/chatbot/step'
    data = {
        "session_id": session_id,
        "collection_name": "collection_93423009_379f_4a90_99ac_cc42ca875f57",
        "user_input": user_input,
        "step": step,
        "user_response": {
            "caption": caption,
            "value": value
        },
        "response_type": "",
        "followup_yes": "",
        "followup_no": "",
        "criteria_list": []
    }
    
    try:
        response = requests.post(url, json=data, timeout=10)
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Step {step} successful")
            print(f"   Intent: {result.get('intent_name', 'N/A')}")
            print(f"   Response: {result.get('response', 'N/A')[:100]}...")
            print(f"   Next Step: {result.get('step', 'N/A')}")
            return result
        else:
            print(f"❌ Step {step} failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Step {step} error: {e}")
        return None

def main():
    """Main test function"""
    print("🚀 Starting EoDB Chatbot Flow Test")
    print("=" * 50)
    
    # Test 1: Create session
    session_id = test_session_creation()
    if not session_id:
        print("❌ Cannot proceed without session")
        return
    
    print("\n" + "=" * 50)
    
    # Test 2: Initial greeting
    result = test_chatbot_step(session_id, "Choose sector or industry", 1)
    if not result:
        print("❌ Initial step failed")
        return
    
    print("\n" + "=" * 50)
    
    # Test 3: Select option 1 (Apply for licence/clearance)
    result = test_chatbot_step(session_id, "1. Apply for licence/clearance", 1)
    if not result:
        print("❌ Option 1 selection failed")
        return
    
    print("\n" + "=" * 50)
    
    # Test 4: Select option 2 (Know application status)
    session_id2 = test_session_creation()  # Create new session for second test
    if session_id2:
        result = test_chatbot_step(session_id2, "2. Know application status", 1)
        if result:
            print("✅ Option 2 selection successful")
        else:
            print("❌ Option 2 selection failed")
    
    print("\n" + "=" * 50)
    print("🏁 Test completed!")

if __name__ == "__main__":
    main()
