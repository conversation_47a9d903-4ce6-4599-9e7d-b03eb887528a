<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug EoDB Chatbot</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ccc;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .response {
            background: #f0f0f0;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            font-size: 12px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .error {
            color: red;
            background: #ffe6e6;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            color: green;
            background: #e6ffe6;
            padding: 10px;
            border-radius: 3px;
        }
        .warning {
            color: orange;
            background: #fff3cd;
            padding: 10px;
            border-radius: 3px;
        }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
    </style>
</head>
<body>
    <h1>EoDB Chatbot Debug Tool</h1>
    
    <div class="grid">
        <div class="test-section">
            <h3>1. Test PHP Simple</h3>
            <button onclick="testPHPSimple()">Test PHP Basic</button>
            <div id="php-simple-response" class="response"></div>
        </div>

        <div class="test-section">
            <h3>2. Test FastAPI Direct</h3>
            <button onclick="testFastAPIDirect()">Test FastAPI</button>
            <div id="fastapi-response" class="response"></div>
        </div>

        <div class="test-section">
            <h3>3. Test Session Creation</h3>
            <button onclick="testSessionCreation()">Create Session</button>
            <div id="session-response" class="response"></div>
        </div>

        <div class="test-section">
            <h3>4. Test PHP Endpoint</h3>
            <button onclick="testPHPEndpoint()">Test aiSanlaapBotQuestionAnswerEODB</button>
            <div id="php-endpoint-response" class="response"></div>
        </div>

        <div class="test-section">
            <h3>5. Simulate Option 1</h3>
            <button onclick="simulateOption1()">Apply for licence/clearance</button>
            <div id="option1-response" class="response"></div>
        </div>

        <div class="test-section">
            <h3>6. Simulate Option 2</h3>
            <button onclick="simulateOption2()">Know application status</button>
            <div id="option2-response" class="response"></div>
        </div>
    </div>

    <div class="test-section">
        <h3>Debug Console</h3>
        <div id="debug-console" class="response" style="height: 200px;"></div>
        <button onclick="clearDebug()">Clear Console</button>
    </div>

    <script>
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            $('#debug-console').append(`[${timestamp}] ${message}\n`);
            $('#debug-console').scrollTop($('#debug-console')[0].scrollHeight);
        }

        function testPHPSimple() {
            log('Testing PHP Simple...');
            $('#php-simple-response').html('Testing...');
            
            $.ajax({
                url: 'test_simple.php',
                type: 'GET',
                dataType: 'json',
                success: function(data) {
                    $('#php-simple-response').html('<div class="success">PHP is working!</div>' + JSON.stringify(data, null, 2));
                    log('✅ PHP Simple test successful');
                },
                error: function(xhr, status, error) {
                    const errorMsg = `Status: ${status}, Error: ${error}, HTTP: ${xhr.status}, Response: ${xhr.responseText}`;
                    $('#php-simple-response').html('<div class="error">' + errorMsg + '</div>');
                    log('❌ PHP Simple test failed: ' + errorMsg);
                }
            });
        }

        function testFastAPIDirect() {
            log('Testing FastAPI Direct...');
            $('#fastapi-response').html('Testing...');
            
            $.ajax({
                url: 'http://127.0.0.1:8020/chatbot/step',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    "session_id": "test-session",
                    "collection_name": "collection_93423009_379f_4a90_99ac_cc42ca875f57",
                    "user_input": "Choose sector or industry",
                    "step": 1,
                    "user_response": {"caption": "", "value": ""},
                    "response_type": "",
                    "followup_yes": "",
                    "followup_no": "",
                    "criteria_list": []
                }),
                success: function(data) {
                    $('#fastapi-response').html('<div class="success">FastAPI is working!</div>' + JSON.stringify(data, null, 2));
                    log('✅ FastAPI Direct test successful');
                },
                error: function(xhr, status, error) {
                    const errorMsg = `Status: ${status}, Error: ${error}, HTTP: ${xhr.status}`;
                    $('#fastapi-response').html('<div class="error">' + errorMsg + '</div>');
                    log('❌ FastAPI Direct test failed: ' + errorMsg);
                }
            });
        }

        function testSessionCreation() {
            log('Testing Session Creation...');
            $('#session-response').html('Testing...');
            
            $.ajax({
                url: 'http://127.0.0.1:8020/session_manager',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    "session_type": "session_create",
                    "session_id": ""
                }),
                success: function(data) {
                    $('#session-response').html('<div class="success">Session created!</div>' + JSON.stringify(data, null, 2));
                    log('✅ Session creation successful: ' + data.session_id);
                },
                error: function(xhr, status, error) {
                    const errorMsg = `Status: ${status}, Error: ${error}, HTTP: ${xhr.status}`;
                    $('#session-response').html('<div class="error">' + errorMsg + '</div>');
                    log('❌ Session creation failed: ' + errorMsg);
                }
            });
        }

        function testPHPEndpoint() {
            log('Testing PHP Endpoint...');
            $('#php-endpoint-response').html('Testing...');
            
            $.ajax({
                url: 'aiSanlaapBotQuestionAnswerEODB',
                type: 'POST',
                dataType: 'json',
                data: {
                    question: 'test',
                    language: '1'
                },
                success: function(data) {
                    $('#php-endpoint-response').html('<div class="success">PHP Endpoint is working!</div>' + JSON.stringify(data, null, 2));
                    log('✅ PHP Endpoint test successful');
                },
                error: function(xhr, status, error) {
                    const errorMsg = `Status: ${status}, Error: ${error}, HTTP: ${xhr.status}, Response: ${xhr.responseText}`;
                    $('#php-endpoint-response').html('<div class="error">' + errorMsg + '</div>');
                    log('❌ PHP Endpoint test failed: ' + errorMsg);
                }
            });
        }

        function simulateOption1() {
            log('Simulating Option 1...');
            $('#option1-response').html('Testing...');
            
            $.ajax({
                url: 'aiSanlaapBotQuestionAnswerEODB',
                type: 'POST',
                dataType: 'json',
                data: {
                    question: '1. Apply for licence/clearance',
                    language: '1'
                },
                success: function(data) {
                    $('#option1-response').html('<div class="success">Option 1 successful!</div>' + JSON.stringify(data, null, 2));
                    log('✅ Option 1 simulation successful');
                },
                error: function(xhr, status, error) {
                    const errorMsg = `Status: ${status}, Error: ${error}, HTTP: ${xhr.status}, Response: ${xhr.responseText}`;
                    $('#option1-response').html('<div class="error">' + errorMsg + '</div>');
                    log('❌ Option 1 simulation failed: ' + errorMsg);
                }
            });
        }

        function simulateOption2() {
            log('Simulating Option 2...');
            $('#option2-response').html('Testing...');
            
            $.ajax({
                url: 'aiSanlaapBotQuestionAnswerEODB',
                type: 'POST',
                dataType: 'json',
                data: {
                    question: '2. Know application status',
                    language: '1'
                },
                success: function(data) {
                    $('#option2-response').html('<div class="success">Option 2 successful!</div>' + JSON.stringify(data, null, 2));
                    log('✅ Option 2 simulation successful');
                },
                error: function(xhr, status, error) {
                    const errorMsg = `Status: ${status}, Error: ${error}, HTTP: ${xhr.status}, Response: ${xhr.responseText}`;
                    $('#option2-response').html('<div class="error">' + errorMsg + '</div>');
                    log('❌ Option 2 simulation failed: ' + errorMsg);
                }
            });
        }

        function clearDebug() {
            $('#debug-console').html('');
        }

        // Initialize
        $(document).ready(function() {
            log('Debug tool initialized');
        });
    </script>
</body>
</html>
